/* ==UserStyle==
@name           The Unbeveling - Fixed & Optimized
@namespace      youtube.com
@version        5.1.0
@description    YouTube with squares - All errors fixed, performance optimized
<AUTHOR> (Fixed & Optimized by AI)
@preprocessor   stylus
@advanced range smallrad "Set Radius" [3, 0, 12, 1, "px"]
@advanced checkbox skeumorphism "Skeumorhic stuff (alpha)" 0
@advanced select b1 "Hamburger"{
    "d1:Darker":"var(--yt-spec-general-background-a)",
    "m1:Default*":"rgba(0,0,0,0)",
    "l1:Lighter":"var(--ytd-searchbox-legacy-button-color)"
}
@advanced select b2 "Microphone"{
    "d2:Darker":"var(--yt-spec-general-background-a)",
    "m2:Default":"rgba(0,0,0,0)",
    "l2:Lighter*":"var(--ytd-searchbox-legacy-button-color)"
}
@advanced select b3 "Upload & Notifications"{
    "d3:Darker":"var(--yt-spec-general-background-a)",
    "m3:Default*":"rgba(0,0,0,0)",
    "l3:Lighter":"var(--ytd-searchbox-legacy-button-color)"
}

@advanced checkbox circlemic "Circular Microphone Button" 1
@advanced checkbox roundmag "Oblong Search" 1
@advanced checkbox oblongplaylist "Oblong Playlist" 1
@advanced checkbox rounddescbuttons "Rounded Description Buttons" 0
@advanced checkbox ttimesleek "Thumbnail Duration Modifier" 1
@advanced checkbox bubblenotif "Bubble Notification shape" 0
@advanced range sidemenround "Side Menu Rounding" [20, 0, 20, 1, "px"]
@advanced checkbox snfc "Use Special Notification Flag Color" 0
@advanced color notiflagcolor "Flag Color" #e10000
@advanced checkbox snfcb "Use Special Notification Flag Border Color" 0
@advanced color notiflagcolorb "Border Color" #fff
@advanced checkbox sbmute "Muted Color Sponsor Button" 0
@advanced checkbox menucolor "Menu (dark only)" 1
@advanced checkbox hidechat "Hide private premiere chat" 1
@advanced checkbox squaredonos "Square stream donations" 1
@advanced checkbox roundedplayer "Don't round player" 1
@advanced checkbox chatpinhover "Transparent chat pin" 1
@advanced checkbox disabledistraction "Disable Video Hover Glow" 1
@advanced checkbox roundsponsorblock "Round Sponsorblock disclaimer" 0

==/UserStyle== */

@-moz-document domain("youtube.com") {

/* ========================================
   CORE VARIABLES & CONFIGURATION
   ======================================== */

:root {
    /* Core radius variables */
    --yt-live-chat-poll-choice-text-padding: 8px;
    --yt-img-border-radius: smallrad !important;
    --smallrad2x: calc(smallrad * 2);
    --smallrad4x: calc(smallrad * 4);

    /* Brand colors - optimized for consistency */
    --yt-spec-static-brand-red: #f00 !important;
    --accent-color: #c00 !important;
    --light-accent-color: #c00 !important;
    --dark-accent-color: #c00 !important;
    --yt-spec-static-overlay-background-brand: rgba(225, 0, 0, 0.9) !important;
    --yt-spec-red-indicator: #f00;
}

/* ========================================
   GRID LAYOUT OPTIMIZATIONS
   ======================================== */

/* Experimental grid layout - 6 videos per row optimization */
ytd-rich-item-renderer[rendered-from-rich-grid],
ytd-video-renderer[use-bigger-thumbs][bigger-thumbs-style=BIG] ytd-thumbnail.ytd-video-renderer {
    min-width: 252px;
    max-width: calc(29.4vw - 252px); /* Edit this value for grid sizing */
}

/* Grid item margins */
ytd-rich-item-renderer[rendered-from-rich-grid][is-in-first-column],
ytd-rich-item-renderer {
    margin: 0 4px 4px;
}

.video-badge.ytd-rich-grid-media {
    margin: 0;
}

#contents.ytd-rich-grid-renderer {
    padding: 0 0 0 4px;
}

/* Disable video hover glow effect */
ytd-rich-item-renderer {
    background-color: transparent !important;
    box-shadow: none !important;
}

/* ========================================
   PLAYER OPTIMIZATIONS
   ======================================== */

/* Video player border radius */
ytd-watch-flexy[rounded-player-large]:not([fullscreen]):not([theater]) #ytd-player.ytd-watch-flexy {
    border-radius: smallrad;
}

/* Remove player rounding when option is disabled */
ytd-watch-flexy[rounded-player-large]:not([fullscreen]):not([theater]) #ytd-player.ytd-watch-flexy,
ytd-watch-flexy[rounded-player] #ytd-player.ytd-watch-flexy {
    border-radius: 0;
}

/* Fix video time display styling */
.ytp-xs-mono-button-style .ytp-time-wrapper:not(.ytp-miniplayer-ui *) {
    background-color: transparent;
    border-radius: 0;
}

.ytp-time-display.ytp-xs-mono-button-style:not(.ytp-miniplayer-ui *),
.ytp-delhi-modern .ytp-time-display:not(.ytp-miniplayer-ui *) {
    font-weight: 400;
}

/* ========================================
   THUMBNAIL OPTIMIZATIONS
   ======================================== */

/* Skip navigation button */
#skip-navigation .yt-spec-button-shape-next--mono.yt-spec-button-shape-next--tonal {
    border-radius: smallrad;
}

/* Large thumbnails */
ytd-thumbnail[size=large] a.ytd-thumbnail,
ytd-thumbnail[size=large]:before,
.yt-thumbnail-view-model--large,
.ytp-videowall-still-round-medium .ytp-videowall-still-image,
.ytp-videowall-still-round-large .ytp-videowall-still-image,
.ytp-videowall-still-info-content {
    border-radius: smallrad;
}

/* Video endcards */
.ytp-ce-video.ytp-ce-large-round,
.ytp-ce-playlist.ytp-ce-large-round {
    border-radius: 1px;
}

/* Medium thumbnails */
ytd-thumbnail[size=medium] a.ytd-thumbnail,
ytd-thumbnail[size=medium]:before,
.yt-thumbnail-view-model--medium,
.collections-stack-wiz__collection-stack2,
.collections-stack-wiz__collection-stack1--large,
.collections-stack-wiz__collection-stack1--medium {
    border-radius: smallrad;
}

/* Collection stack positioning */
.collections-stack-wiz__collection-stack1--medium {
    left: smallrad;
    right: smallrad;
    width: calc(100% - var(--smallrad2x));
}

.collections-stack-wiz__collection-stack2 {
    left: calc(smallrad + smallrad);
    right: calc(smallrad + smallrad);
    width: calc(100% - var(--smallrad4x));
}

/* Dismissed content styling */
ytd-rich-grid-media[rounded-container] #dismissed.ytd-rich-grid-media {
    border-radius: smallrad !important;
}

#dismissed.ytd-rich-grid-media,
#dismissed.ytd-compact-video-renderer,
ytd-feed-nudge-renderer[rounded-container] #dismissed.ytd-feed-nudge-renderer,
ytd-feed-nudge-renderer[rounded-container] #dismissible.ytd-feed-nudge-renderer {
    border-radius: smallrad;
}

/* ========================================
   THUMBNAIL OVERLAYS & TIME STATUS
   ======================================== */

/* Default thumbnail overlays */
#time-status.ytd-thumbnail-overlay-time-status-renderer,
ytd-thumbnail-overlay-bottom-panel-renderer[use-modern-collections-v2],
ytd-thumbnail-overlay-bottom-panel-renderer {
    border-radius: smallrad;
}

ytd-thumbnail-overlay-bottom-panel-renderer[use-modern-collections-v2],
ytd-thumbnail-overlay-bottom-panel-renderer {
    margin-bottom: 3px;
    margin-right: 3px;
}

ytd-thumbnail[size] ytd-thumbnail-overlay-time-status-renderer.ytd-thumbnail,
ytd-thumbnail[size] ytd-thumbnail-overlay-button-renderer.ytd-thumbnail,
ytd-thumbnail-overlay-bottom-panel-renderer[use-modern-collections-v2] {
    margin: 4px;
}

/* Live badge styling */
.badge-shape-wiz--thumbnail-live {
    background: #c00;
}

/* SponsorBlock integration */
.sponsorThumbnailLabel,
.sponsorThumbnailLabel:hover {
    border-radius: smallrad;
}

/* Sleek thumbnail duration styling */
ytd-thumbnail[size] ytd-thumbnail-overlay-time-status-renderer.ytd-thumbnail,
ytd-thumbnail[size] ytd-thumbnail-overlay-button-renderer.ytd-thumbnail,
ytd-thumbnail-overlay-bottom-panel-renderer[use-modern-collections-v2],
.yt-thumbnail-overlay-badge-view-model-wiz--medium,
.yt-thumbnail-overlay-badge-view-model-wiz--large {
    margin: 0;
}

#time-status.ytd-thumbnail-overlay-time-status-renderer,
ytd-thumbnail-overlay-bottom-panel-renderer[use-modern-collections-v2] {
    border-radius: smallrad 0 smallrad 0;
}

.ytp-videowall-still-info-live {
    right: 0;
    bottom: 0;
    border-radius: smallrad 0 smallrad 0;
}

.sponsorThumbnailLabel,
.sponsorThumbnailLabel:hover {
    margin: 0;
    border-radius: smallrad 0 smallrad 0;
}

/* ========================================
   AVATAR & PROFILE PICTURE OPTIMIZATIONS
   ======================================== */

/* Navigation bar avatars */
#avatar,
yt-img-shadow.ytd-topbar-menu-button-renderer {
    border-radius: smallrad !important;
}

.yt-spec-button-shape-next--call-to-action.yt-spec-button-shape-next--outline {
    border-radius: smallrad !important;
}

/* Channel member avatars */
.avatar.ytd-recognition-shelf-renderer {
    border-radius: smallrad !important;
    margin-right: 8px;
}

#avatars-container.ytd-recognition-shelf-renderer {
    margin-right: 0;
}

/* Channel page avatars */
yt-img-shadow.ytd-grid-channel-renderer,
yt-img-shadow.ytd-channel-renderer {
    border-radius: smallrad !important;
}

/* Members-only content avatars */
.html5-ypc-thumbnail img.avatar {
    border-radius: smallrad !important;
}

/* Disabled button styling */
.yt-spec-button-shape-next--disabled.yt-spec-button-shape-next--tonal {
    background-color: transparent;
}

/* Search results avatars */
yt-img-shadow.ytd-video-renderer {
    border-radius: smallrad;
}

/* ========================================
   BUTTON & INTERACTION OPTIMIZATIONS
   ======================================== */

/* General button styling */
#guide-button.ytd-masthead,
yt-icon-button.ytd-masthead,
ytd-topbar-menu-button-renderer #button.ytd-topbar-menu-button-renderer,
yt-interaction.circular .fill.yt-interaction,
yt-interaction.circular .stroke.yt-interaction,
.yt-spec-button-shape-next--icon-only-default,
yt-icon-button.ytd-notification-topbar-button-renderer,
ytd-topbar-menu-button-renderer.style-default[is-icon-button] {
    border-radius: smallrad;
}

/* Voice search button - conditional circular styling */
#voice-search-button.ytd-masthead {
    border-radius: 5px;
}

/* ========================================
   SEARCH BAR OPTIMIZATIONS
   ======================================== */

/* Search container styling */
ytd-searchbox[desktop-searchbar-style=rounded_corner_borders_light_btn] #container.ytd-searchbox,
ytd-searchbox[desktop-searchbar-style=rounded_corner_autofocus] #container.ytd-searchbox {
    border-radius: 2px 0 0 2px;
}

/* Left side of search bar */
ytd-searchbox[desktop-searchbar-style=rounded_corner_autofocus] #container.ytd-searchbox,
#container.ytd-searchbox,
.ytSearchboxComponentInputBox {
    border-top-left-radius: smallrad;
    border-bottom-left-radius: smallrad;
}

/* Right side of search bar */
ytd-searchbox[desktop-searchbar-style=rounded_corner_autofocus] #search-icon-legacy.ytd-searchbox {
    border-top-right-radius: smallrad;
    border-bottom-right-radius: smallrad;
}

/* Search button styling */
#search-icon-legacy.ytd-searchbox,
.ytSearchboxComponentSearchButton {
    border-top-right-radius: 2px !important;
    border-bottom-right-radius: 2px !important;
}

/* Search dropdown styling */
.ytSearchboxComponentInputBoxDark.ytSearchboxComponentInputBoxHasFocus,
ytd-searchbox[has-focus] #container.ytd-searchbox {
    border-bottom-left-radius: 0;
}

.sbsb_a,
.ytSearchboxComponentSuggestionsContainer {
    border-radius: 0 0 smallrad smallrad;
    padding: 8px 0 0;
    border: 1px solid hsl(0, 0%, 18.82%);
    border-top: 0;
    top: 40px;
}

.ytSuggestionComponentLargerSuggestion,
.ytSuggestionComponentSuggestion {
    height: 32px !important;
}

.gstl_50 {
    top: 48px !important;
}

.ytSearchboxComponentReportButton {
    margin-top: 3px;
    margin-bottom: 3px;
}

/* Search clear button */
.ytSearchboxComponentDesktop .ytSearchboxComponentClearButton {
    border-radius: 0;
    width: 38px;
    height: 38px;
    right: 3px;
}

/* ========================================
   SIDEBAR & NAVIGATION OPTIMIZATIONS
   ======================================== */

/* Left sidebar navigation */
ytd-guide-entry-renderer[guide-refresh],
ytd-guide-entry-renderer[guide-refresh] #endpoint.yt-simple-endpoint.ytd-guide-entry-renderer:hover,
ytd-guide-entry-renderer[guide-refresh] yt-interaction.ytd-guide-entry-renderer,
#endpoint.yt-simple-endpoint.ytd-guide-entry-renderer,
yt-interaction.ytd-guide-entry-renderer,
ytd-guide-entry-renderer {
    border-radius: 0px sidemenround sidemenround 0px !important;
}

ytd-guide-section-renderer {
    padding-left: 0 !important;
}

#newness-dot.ytd-guide-entry-renderer {
    width: 8px;
    border-radius: 2px;
}

ytd-mini-guide-entry-renderer {
    border-radius: 5px;
}

/* Subscription entries */
yt-img-shadow.ytd-guide-entry-renderer {
    border-radius: smallrad !important;
}

/* Touch feedback styling */
.yt-spec-touch-feedback-shape--touch-response {
    opacity: 0.5;
    border-radius: smallrad;
}

/* ========================================
   COMMENT SYSTEM OPTIMIZATIONS
   ======================================== */

/* Comment author thumbnails */
#author-thumbnail.ytd-comment-renderer yt-img-shadow.ytd-comment-renderer,
#author-thumbnail.ytd-comment-simplebox-renderer,
#author-thumbnail.ytd-commentbox,
ytd-commentbox[is-reply][is-backstage-comment] #author-thumbnail.ytd-commentbox,
#author-thumbnail.ytd-comment-view-model yt-img-shadow.ytd-comment-view-model {
    border-radius: smallrad !important;
}

/* Reply button styling */
#reply-button-end .yt-spec-button-shape-next--mono.yt-spec-button-shape-next--text {
    background-color: transparent;
}

/* Comment button styling */
.ytd-commentbox .yt-spec-button-shape-next--filled {
    margin-left: 5px;
}

/* Enlarge sub-commenter avatars */
ytd-comment-renderer:not([comment-style=backstage-comment])[is-reply] #author-thumbnail.ytd-comment-renderer yt-img-shadow.ytd-comment-renderer {
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

/* Post author thumbnails */
#author-thumbnail.ytd-post-renderer yt-img-shadow.ytd-post-renderer,
ytd-comment-replies-renderer #creator-thumbnail.ytd-comment-replies-renderer yt-img-shadow.ytd-comment-replies-renderer,
#hearted-thumbnail.ytd-creator-heart-renderer,
ytd-comments-entry-point-header-renderer:not([modern-metapanel]) .avatar.ytd-comments-entry-point-header-renderer,
ytd-post-renderer[rounded-container] {
    border-radius: smallrad !important;
}

/* Backstage post styling */
#author-thumbnail.ytd-backstage-post-renderer yt-img-shadow.ytd-backstage-post-renderer,
ytd-backstage-post-thread-renderer[rounded-container],
img.yt-img-shadow,
ytd-backstage-image-renderer[rounded] #image-container.ytd-backstage-image-renderer yt-img-shadow.ytd-backstage-image-renderer,
yt-img-shadow.ytd-backstage-image-renderer {
    border-radius: smallrad !important;
}

ytd-backstage-post-renderer[uses-full-lockup] {
    border-radius: smallrad;
    padding: 8px 8px 0;
}

ytd-backstage-post-thread-renderer {
    margin-top: 12px;
}

ytd-backstage-post-thread-renderer:nth-child(1) {
    margin-top: 0;
}

/* ========================================
   LIVE CHAT OPTIMIZATIONS
   ======================================== */

/* Live chat author photos */
#author-photo.yt-live-chat-text-message-renderer,
#author-photo.yt-live-chat-paid-message-renderer,
#author-photo.yt-live-chat-membership-item-renderer,
#author-photo.ytd-sponsorships-live-chat-gift-redemption-announcement-renderer {
    border-radius: smallrad;
    margin-right: 8px;
}

#header.yt-live-chat-membership-item-renderer {
    padding: 8px;
}

/* Live chat frame styling */
ytd-live-chat-frame[rounded-container],
ytd-live-chat-frame[rounded-container] #show-hide-button.ytd-live-chat-frame ytd-toggle-button-renderer.ytd-live-chat-frame,
#creator-photo.yt-live-chat-banner-redirect-renderer {
    border-radius: smallrad;
}

ytd-live-chat-frame[rounded-container] iframe.ytd-live-chat-frame {
    border-radius: 0;
}

/* Paid message styling */
#card.yt-live-chat-paid-message-renderer,
yt-live-chat-paid-message-renderer[is-v2-style] #header.yt-live-chat-paid-message-renderer,
yt-live-chat-paid-message-renderer[is-v2-style] #content.yt-live-chat-paid-message-renderer {
    border-radius: smallrad;
}

#buy-flow-button.yt-live-chat-paid-message-renderer yt-button-renderer.yt-live-chat-paid-message-renderer {
    margin-top: 8px;
}

#header.yt-live-chat-paid-message-renderer {
    padding: 8px 8px 0 0;
}

#content.yt-live-chat-paid-message-renderer {
    padding: 8px;
}

/* ========================================
   SUBSCRIPTION & BUTTON STYLING
   ======================================== */

/* Subscribe button styling */
.yt-spec-button-shape-next--mono.yt-spec-button-shape-next--filled {
    border-radius: smallrad;
}

/* Remove superfluous button borders */
.yt-spec-button-shape-next--mono.yt-spec-button-shape-next--tonal {
    background-color: transparent !important;
}

.yt-spec-button-shape-next--size-m.yt-spec-button-shape-next--segmented-start::after {
    width: 0;
}

/* SponsorBlock integration */
.sponsorSkipObject {
    margin-left: 0;
}

.sponsorBlockCategoryPill {
    border-radius: smallrad;
}

/* ========================================
   DESCRIPTION & METADATA STYLING
   ======================================== */

/* Description background */
ytd-watch-metadata[modern-metapanel] #description.ytd-watch-metadata,
#description.ytd-watch-metadata {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 0 0 12px 12px !important;
}

.ytVideoMetadataCarouselViewModelHost {
    background: rgba(0, 0, 0, 0.3);
}

#description.ytd-watch-metadata {
    margin-top: 0;
}

.yt-spec-button-shape-next--call-to-action.yt-spec-button-shape-next--text {
    border-radius: smallrad;
}

/* Description cards */
ytd-structured-description-playlist-lockup-renderer[rounded] #playlist-thumbnail.ytd-structured-description-playlist-lockup-renderer,
ytd-media-lockup-renderer[is-compact] #thumbnail-container.ytd-media-lockup-renderer,
.yt-spec-place-data-view-model-shape__thumbnail-round {
    border-radius: smallrad;
}

/* Channel lockup in description */
ytd-thumbnail.ytd-structured-description-channel-lockup-renderer {
    border-radius: smallrad;
}

#thumbnail-container.ytd-structured-description-channel-lockup-renderer {
    justify-content: flex-end;
}

/* Hide redundant video info */
#header.ytd-video-description-infocards-section-renderer,
#action-buttons.ytd-video-description-infocards-section-renderer {
    display: none;
}

#collapse.ytd-text-inline-expander {
    margin-top: 0;
}

ytd-video-description-infocards-section-renderer {
    padding: 16px 0 0 0;
}

.arrow-container.ytd-video-description-infocards-section-renderer {
    background-color: var(--yt-spec-additive-background);
}

/* Description button styling */
.yt-core-attributed-string--highlight-text-decorator {
    border-radius: smallrad !important;
}

/* Music metadata styling */
#topic-link-container.ytd-topic-link-renderer {
    width: 20%;
    min-width: 300px;
}

#default-metadata-section.ytd-info-row-renderer {
    margin-left: 0;
}

/* ========================================
   CHIP & CATEGORY STYLING
   ======================================== */

/* Category chips */
yt-chip-cloud-chip-renderer,
.YtChipShapeChip,
#chip-container.yt-chip-cloud-chip-renderer {
    border-radius: smallrad !important;
    margin: 2.5px 0 7.5px 2.5px !important;
    height: 28px;
}

yt-chip-cloud-renderer:not([no-top-margin]) yt-chip-cloud-chip-renderer.yt-chip-cloud-renderer {
    margin: 0 5px 5px 0 !important;
}

ytd-feed-filter-chip-bar-renderer[component-style=FEED_FILTER_CHIP_BAR_STYLE_TYPE_CHANNEL_PAGE_GRID] yt-chip-cloud-chip-renderer.ytd-feed-filter-chip-bar-renderer:first-of-type {
    margin-left: 0 !important;
}

/* ========================================
   PANEL & SECTION STYLING
   ======================================== */

/* Video info panels */
#chat.ytd-watch-flexy,
#donation-shelf.ytd-watch-flexy ytd-donation-shelf-renderer.ytd-watch-flexy,
#donation-shelf.ytd-watch-flexy ytd-donation-unavailable-renderer.ytd-watch-flexy,
#playlist.ytd-watch-flexy,
#panels.ytd-watch-flexy ytd-engagement-panel-section-list-renderer.ytd-watch-flexy {
    border-radius: smallrad;
    margin-bottom: 8px;
}

/* Section list styling */
ytd-section-list-renderer:not([hide-bottom-separator]):not([page-subtype=history]):not([page-subtype=memberships-and-purchases]):not([page-subtype=ypc-offers]):not([live-chat-engagement-panel]) #contents.ytd-section-list-renderer > *.ytd-section-list-renderer:not(:last-child):not(ytd-page-introduction-renderer):not([item-dismissed]):not([has-destination-shelf-renderer]):not(ytd-minor-moment-header-renderer):not([has-section-group-view-model]) {
    margin-bottom: 12px;
    border-bottom: 0;
}

/* Transcript styling */
ytd-transcript-segment-list-renderer {
    background: var(--yt-spec-base-background);
}

ytd-transcript-segment-renderer[rounded-container] .segment-timestamp.ytd-transcript-segment-renderer {
    border-radius: smallrad;
}

/* Donation shelf */
ytd-donation-shelf-renderer[modern-panels] {
    border-radius: smallrad;
}

/* Ticket shelf */
ytd-ticket-shelf-renderer {
    border-bottom: 0;
    padding-bottom: 0;
}

/* ========================================
   PLAYLIST STYLING
   ======================================== */

/* Playlist panel */
ytd-playlist-panel-renderer[modern-panels]:not([within-miniplayer]) #container.ytd-playlist-panel-renderer {
    border-radius: smallrad;
}

ytd-playlist-panel-renderer[collapsible] .header.ytd-playlist-panel-renderer {
    padding: 10px 6px 0 16px;
}

#playlist.ytd-watch-flexy:not([js-panel-height_]).ytd-watch-flexy:not([disable-upgrade]).ytd-watch-flexy {
    margin: 0 0 12px 0;
}

ytd-watch-flexy[cinematics-enabled] #secondary.ytd-watch-flexy {
    margin: 8px 0 0 0;
}

ytd-watch-flexy[default-layout]:not([no-top-margin]):not([reduced-top-margin]) #secondary.ytd-watch-flexy {
    padding-top: 16px;
}

ytd-playlist-video-renderer[amsterdam] {
    border-radius: smallrad;
}

ytd-add-to-playlist-renderer[dialog] #playlists.ytd-add-to-playlist-renderer {
    background: var(--yt-spec-base-background);
}

/* ========================================
   NOTIFICATION & POPUP STYLING
   ======================================== */

/* Notification menus */
ytd-multi-page-menu-renderer[menu-style=multi-page-menu-style-type-comments],
ytd-multi-page-menu-renderer[menu-style=multi-page-menu-style-type-notifications] {
    border-radius: smallrad;
    opacity: 0.95;
}

/* Notification thumbnails */
.thumbnail-container.ytd-notification-renderer {
    border-radius: smallrad;
    opacity: 1;
}

.thumbnail.ytd-notification-renderer,
.text.ytd-notification-renderer {
    opacity: 1;
}

/* Notification badge */
.yt-spec-icon-badge-shape__badge {
    border-radius: 5px;
}

/* Bubble notification styling */
.yt-spec-icon-badge-shape__badge {
    border-radius: 8px 8px 8px 0;
}

/* ========================================
   DROPDOWN & MENU STYLING
   ======================================== */

/* Dropdown menus */
yt-dropdown-menu[modern-dialogs] #menu.yt-dropdown-menu,
ytd-menu-popup-renderer[sheets-refresh],
ytd-menu-popup-renderer[sheets-refresh] tp-yt-paper-listbox.ytd-menu-popup-renderer,
tp-yt-paper-menu-button[vertical-align=top] .dropdown-content.tp-yt-paper-menu-button,
ytd-menu-popup-renderer {
    border-radius: smallrad;
    padding: 0;
}

.yt-sheet-view-model-wiz--contextual,
.yt-list-item-view-model-wiz,
.yt-list-item-view-model-wiz__container--tappable:hover {
    border-radius: smallrad !important;
}

tp-yt-paper-listbox {
    padding: 0;
}

ytd-menu-service-item-renderer[has-separator]:not(:last-child)::after {
    margin: 0;
}

#buttons.ytd-dismissal-follow-up-renderer ytd-button-renderer.ytd-dismissal-follow-up-renderer {
    margin-left: 4px !important;
}

/* User dropdown */
ytd-multi-page-menu-renderer {
    border-radius: smallrad;
}

#sections.ytd-multi-page-menu-renderer > *.ytd-multi-page-menu-renderer {
    padding: 0;
}

ytd-active-account-header-renderer {
    padding: 8px 16px;
}

#avatar.ytd-active-account-header-renderer {
    margin-top: 3px;
}

/* ========================================
   ENDCARD & VIDEO OVERLAY STYLING
   ======================================== */

/* Endcards */
.ytp-ce-channel,
.ytp-ce-channel .ytp-ce-expanding-image,
.ytp-ce-channel .ytp-ce-element-shadow {
    border-radius: 0.8pc;
}

.ytp-settings-menu,
.ytp-screen-mode-menu {
    border-radius: smallrad;
}

/* Channel trailer endcard */
.subscribecard-endscreen .ytp-author-image {
    border-radius: smallrad;
}

/* Side card elements */
.iv-card.ytp-rounded-info {
    border-radius: 0.3pc;
}

/* General thumbnail styling */
#thumbnail {
    border-radius: smallrad !important;
}

/* Endcard elements */
.ytp-ce-element.ytp-ce-element-show {
    border-radius: 0.5pc;
}

.ytp-ce-video-duration {
    border-radius: 0.5pc 0 0 0;
    margin: 0 !important;
}

.ytp-ce-element.ytp-ce-element-show:has(.ytp-ce-expanding-image) {
    border-radius: 1pc;
}

/* End of stream */
.ytp-autonav-endscreen-upnext-thumbnail {
    border-radius: smallrad;
}

/* Next video preview */
.ytp-tooltip.ytp-text-detail.ytp-preview,
.ytp-tooltip.ytp-text-detail.ytp-preview .ytp-tooltip-bg {
    border-radius: smallrad;
}

/* Video interaction hover */
ytd-watch-metadata[clickable-description][description-collapsed] #description.ytd-watch-metadata:hover,
yt-interaction.rounded-large .fill.yt-interaction,
yt-interaction.rounded-large .stroke.yt-interaction {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 0 0 12px 12px !important;
}

/* ========================================
   TOOLTIP & POPUP STYLING
   ======================================== */

/* Tooltips */
html body[rounded-container] tp-yt-paper-tooltip .tp-yt-paper-tooltip[style-target=tooltip] {
    border-radius: smallrad;
}

yt-notification-action-renderer[ui-refresh] tp-yt-paper-toast.yt-notification-action-renderer,
tp-yt-paper-toast.yt-notification-action-renderer {
    border-radius: smallrad;
}

/* Dismissal styling */
#buttons.ytd-dismissal-follow-up-renderer {
    margin-top: 0;
}

#content.ytd-dismissal-follow-up-renderer {
    margin: 16px;
}

ytd-dismissal-reason-text-renderer.ytd-dismissal-follow-up-renderer,
ytd-dismissal-reason-video-renderer.ytd-dismissal-follow-up-renderer {
    margin: 16px 0;
}

.tp-yt-paper-checkbox[style-target=label] {
    padding-left: 12px;
}

/* Survey prompt */
#dismissible.ytd-slimline-survey-renderer {
    border-radius: smallrad;
}

/* ========================================
   BRANDING & MERCHANDISING
   ======================================== */

/* Video branding watermark */
.iv-branding .branding-img-container img,
.iv-branding .branding-context-container-inner,
.branding-context-container-inner.ytp-rounded-branding-context,
.ytp-sb-subscribe.ytp-sb-rounded,
.ytp-sb-unsubscribe.ytp-sb-rounded,
.ytp-big-mode .ytp-sb-subscribe.ytp-sb-rounded,
.ytp-big-mode .ytp-sb-unsubscribe.ytp-sb-rounded,
.ytp-sb-subscribe,
.ytp-sb-unsubscribe,
.ytp-big-mode .ytp-sb-subscribe,
.ytp-big-mode .ytp-sb-unsubscribe {
    border-radius: smallrad;
}

/* Merchandising pop-ups */
.ytp-suggested-action-badge,
.ytp-suggested-action-product-thumbnail,
.ytp-featured-product.ytp-suggested-action-badge,
.ytp-featured-product-thumbnail {
    border-radius: smallrad;
}

.ytp-shopping-product-menu {
    border-radius: smallrad;
}

.ytp-suggested-action-product-thumbnail {
    border-radius: 2px;
}

.ytp-suggested-action-badge-content-forward.ytp-suggested-action-badge-expanded .ytp-suggested-action-more-products-icon {
    display: none;
}

/* ========================================
   PERFORMANCE OPTIMIZATIONS
   ======================================== */

/* Consolidated button sizing */
.yt-spec-button-shape-next--size-m,
.yt-spec-button-shape-next--size-s {
    border-radius: smallrad;
}

/* Consolidated dialog styling */
tp-yt-paper-dialog,
yt-mealbar-promo-renderer[has-full-height-image] #icon.yt-mealbar-promo-renderer {
    border-radius: smallrad !important;
}

yt-tooltip-renderer[rounded-container],
yt-tooltip-renderer {
    border-radius: smallrad !important;
}

/* ========================================
   BRAND COLOR OVERRIDES
   ======================================== */

/* Live badge colors */
.badge-shape-wiz--thumbnail-live {
    background: #c00;
}

/* Progress bar colors */
.ytp-play-progress.ytp-swatch-background-color,
.ytThumbnailOverlayProgressBarHostWatchedProgressBarSegmentModern {
    background: #f00 !important;
}

/* Live ring styling */
.yt-spec-avatar-shape--cairo-refresh.yt-spec-avatar-shape--live-ring::after {
    background: #c00;
    border-radius: calc(smallrad * 1.5);
}

/* ========================================
   LAYOUT & SPACING OPTIMIZATIONS
   ======================================== */

/* Compact margins for better space utilization */
#rich-shelf-header.ytd-rich-shelf-renderer {
    margin: 16px 8px;
}

#contents.ytd-rich-shelf-renderer ytd-rich-item-renderer.ytd-rich-shelf-renderer,
#content.ytd-rich-section-renderer > *.ytd-rich-section-renderer,
ytd-rich-item-renderer {
    margin-bottom: 20px;
}

/* Channel header optimization */
#channel-header.ytd-c4-tabbed-header-renderer {
    padding: 0;
}

/* Comments header */
#title.ytd-comments-header-renderer {
    margin-bottom: 12px;
}

/* ========================================
   CONDITIONAL FEATURE STYLING
   ======================================== */

/* Menu color theming */
#sections.ytd-guide-renderer {
    background-color: rgba(255, 255, 255, 0.03);
}

#guide-inner-content.ytd-app {
    background-color: #1b1b1b;
}

#background.ytd-masthead {
    background-color: #1b1b1b;
}

ytd-feed-filter-chip-bar-renderer[darker-dark-theme] #chips-wrapper.ytd-feed-filter-chip-bar-renderer {
    background-color: #1b1b1b;
}

.yt-spec-icon-badge-shape--style-overlay .yt-spec-icon-badge-shape__badge {
    border: 2px solid #1b1b1b;
}

/* Button background colors */
#guide-button.ytd-masthead {
    background-color: b1;
}

#voice-search-button.ytd-masthead {
    background-color: b2;
}

yt-icon-button.ytd-notification-topbar-button-renderer,
ytd-topbar-menu-button-renderer.style-default[is-icon-button] {
    background-color: b3;
}

/* Notification flag colors */
.yt-spec-icon-badge-shape--type-notification .yt-spec-icon-badge-shape__badge {
    background-color: #e10000;
    border: 2px solid #fff;
}

}
